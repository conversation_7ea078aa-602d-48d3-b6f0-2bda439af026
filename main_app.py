# -*- coding: utf-8 -*-
"""
豆包包工助手风格的PyQt6应用程序
现代化UI设计，包含左侧导航、主工作区域、工具栏等
支持AI Agent文档操作对话
"""

import sys
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

# 导入聊天组件
try:
    from chat_widget import ChatWidget
    CHAT_AVAILABLE = True
except ImportError:
    CHAT_AVAILABLE = False
    print("⚠️ 聊天组件未找到，将使用占位符页面")


class ModernCard(QFrame):
    """现代化卡片组件"""
    
    def __init__(self, title="", content="", icon=None):
        super().__init__()
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet("""
            ModernCard {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                margin: 5px;
            }
            ModernCard:hover {
                border: 1px solid #1976d2;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(8)
        
        # 标题栏
        title_layout = QHBoxLayout()
        if icon:
            icon_label = QLabel()
            icon_label.setPixmap(icon.pixmap(24, 24))
            title_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #333333;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        layout.addLayout(title_layout)
        
        # 内容
        content_label = QLabel(content)
        content_label.setWordWrap(True)
        content_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #666666;
                line-height: 1.4;
            }
        """)
        layout.addWidget(content_label)
        
        self.setLayout(layout)


class SideNavigation(QWidget):
    """左侧导航栏"""
    
    navigation_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Logo区域
        logo_widget = QWidget()
        logo_widget.setFixedHeight(80)
        logo_widget.setStyleSheet("""
            QWidget {
                background-color: #1976d2;
                border-bottom: 1px solid #1565c0;
            }
        """)
        
        logo_layout = QHBoxLayout()
        logo_layout.setContentsMargins(20, 0, 20, 0)
        
        logo_label = QLabel("🚀 工作助手")
        logo_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
            }
        """)
        logo_layout.addWidget(logo_label)
        logo_widget.setLayout(logo_layout)
        
        layout.addWidget(logo_widget)
        
        # 导航菜单
        nav_items = [
            ("🏠", "工作台", "dashboard"),
            ("🤖", "AI助手", "chat"),
            ("📝", "文档管理", "documents"),
            ("📊", "数据分析", "analytics"),
            ("⚙️", "工具箱", "tools"),
            ("👥", "团队协作", "team"),
            ("📈", "项目管理", "projects"),
            ("🔧", "系统设置", "settings")
        ]
        
        for icon, text, key in nav_items:
            btn = self.create_nav_button(icon, text, key)
            layout.addWidget(btn)
        
        layout.addStretch()
        
        # 用户信息
        user_widget = self.create_user_info()
        layout.addWidget(user_widget)
        
        self.setLayout(layout)
        
    def create_nav_button(self, icon, text, key):
        """创建导航按钮"""
        button = QPushButton(f"{icon}  {text}")
        button.setFixedHeight(50)
        button.setStyleSheet("""
            QPushButton {
                text-align: left;
                padding: 0 20px;
                border: none;
                background-color: transparent;
                color: #333333;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #f5f5f5;
                color: #1976d2;
            }
            QPushButton:pressed {
                background-color: #e3f2fd;
            }
        """)
        button.clicked.connect(lambda: self.navigation_changed.emit(key))
        return button
        
    def create_user_info(self):
        """创建用户信息区域"""
        widget = QWidget()
        widget.setFixedHeight(70)
        widget.setStyleSheet("""
            QWidget {
                border-top: 1px solid #e0e0e0;
                background-color: #fafafa;
            }
        """)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(20, 10, 20, 10)
        
        # 头像
        avatar = QLabel("👤")
        avatar.setFixedSize(40, 40)
        avatar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        avatar.setStyleSheet("""
            QLabel {
                background-color: #1976d2;
                border-radius: 20px;
                color: white;
                font-size: 18px;
            }
        """)
        
        # 用户信息
        user_info = QVBoxLayout()
        user_info.setSpacing(2)
        
        name_label = QLabel("用户名称")
        name_label.setStyleSheet("font-weight: bold; color: #333333;")
        
        status_label = QLabel("在线")
        status_label.setStyleSheet("color: #666666; font-size: 12px;")
        
        user_info.addWidget(name_label)
        user_info.addWidget(status_label)
        
        layout.addWidget(avatar)
        layout.addLayout(user_info)
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget


class DashboardWidget(QWidget):
    """工作台页面"""
    
    navigate_to_page = pyqtSignal(str)  # 导航信号
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 欢迎标题
        title = QLabel("欢迎使用工作助手")
        title.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #333333;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        subtitle = QLabel("智能办公，高效协作")
        subtitle.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #666666;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(subtitle)
        
        # 统计卡片
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(20)
        
        stats_cards = [
            ("📊", "今日任务", "8个待完成"),
            ("✅", "完成任务", "25个本周"),
            ("👥", "团队成员", "12人在线"),
            ("📈", "工作效率", "提升23%")
        ]
        
        for icon, title, desc in stats_cards:
            card = self.create_stat_card(icon, title, desc)
            stats_layout.addWidget(card)
        
        layout.addLayout(stats_layout)
        
        # 快速操作
        quick_actions = QGroupBox("快速操作")
        quick_actions.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #333333;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        actions_layout = QGridLayout()
        actions_layout.setSpacing(15)
        
        actions = [
            ("🤖", "AI助手", "智能文档操作对话"),
            ("📝", "创建文档", "新建工作文档"),
            ("📊", "数据分析", "生成数据报表"),
            ("🔧", "工具箱", "实用工具集合"),
            ("👥", "团队协作", "邀请成员协作"),
            ("⚙️", "系统设置", "个性化配置")
        ]
        
        for i, (icon, title, desc) in enumerate(actions):
            card = ModernCard(title, desc, None)
            card.setFixedHeight(120)
            card.setProperty("action_key", self.get_action_key(title))
            card.mousePressEvent = lambda event, key=self.get_action_key(title): self.on_card_clicked(key)
            card.setStyleSheet("""
                ModernCard {
                    background-color: #ffffff;
                    border: 1px solid #e0e0e0;
                    border-radius: 8px;
                    margin: 5px;
                    cursor: pointer;
                }
                ModernCard:hover {
                    border: 1px solid #1976d2;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    transform: translateY(-2px);
                }
            """)
            actions_layout.addWidget(card, i // 3, i % 3)
        
        quick_actions.setLayout(actions_layout)
        layout.addWidget(quick_actions)
        
        layout.addStretch()
        self.setLayout(layout)
        
    def get_action_key(self, title: str) -> str:
        """获取操作键名"""
        action_map = {
            "AI助手": "chat",
            "创建文档": "documents", 
            "数据分析": "analytics",
            "工具箱": "tools",
            "团队协作": "team",
            "系统设置": "settings"
        }
        return action_map.get(title, "dashboard")
        
    def on_card_clicked(self, action_key: str):
        """处理卡片点击事件"""
        self.navigate_to_page.emit(action_key)
        
    def create_stat_card(self, icon, title, value):
        """创建统计卡片"""
        card = QFrame()
        card.setFrameStyle(QFrame.Shape.Box)
        card.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.setSpacing(10)
        
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("font-size: 32px;")
        
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #666666;
                font-weight: 500;
            }
        """)
        
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        value_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #1976d2;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(icon_label)
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        card.setLayout(layout)
        return card


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("工作助手 - 智能办公平台")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置窗口图标
        self.setWindowIcon(QIcon("🚀"))
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 左侧导航栏
        self.side_nav = SideNavigation()
        self.side_nav.setFixedWidth(250)
        self.side_nav.setStyleSheet("""
            SideNavigation {
                background-color: #fafafa;
                border-right: 1px solid #e0e0e0;
            }
        """)
        self.side_nav.navigation_changed.connect(self.on_navigation_changed)
        
        # 主工作区域
        self.work_area = QStackedWidget()
        self.work_area.setStyleSheet("""
            QStackedWidget {
                background-color: #f8f9fa;
            }
        """)
        
        # 添加页面
        self.dashboard = DashboardWidget()
        self.dashboard.navigate_to_page.connect(self.on_navigation_changed)  # 连接导航信号
        self.work_area.addWidget(self.dashboard)
        
        # 添加AI聊天页面
        if CHAT_AVAILABLE:
            self.chat_widget = ChatWidget()
            self.work_area.addWidget(self.chat_widget)
        else:
            chat_placeholder = QLabel("🤖 AI助手\n聊天组件加载失败\n请检查依赖项")
            chat_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
            chat_placeholder.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    color: #666666;
                    background-color: white;
                    border-radius: 8px;
                    margin: 20px;
                    padding: 50px;
                }
            """)
            self.work_area.addWidget(chat_placeholder)
        
        # 其他页面占位符
        for name in ["文档管理", "数据分析", "工具箱", "团队协作", "项目管理", "系统设置"]:
            placeholder = QLabel(f"{name}页面\n正在开发中...")
            placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
            placeholder.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    color: #666666;
                    background-color: white;
                    border-radius: 8px;
                    margin: 20px;
                    padding: 50px;
                }
            """)
            self.work_area.addWidget(placeholder)
        
        main_layout.addWidget(self.side_nav)
        main_layout.addWidget(self.work_area, 1)
        
        central_widget.setLayout(main_layout)
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建状态栏
        self.create_statusbar()
        
        # 设置全局样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
        """)
        
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #ffffff;
                border-bottom: 1px solid #e0e0e0;
                spacing: 10px;
                padding: 5px;
            }
            QToolButton {
                background-color: transparent;
                border: 1px solid transparent;
                border-radius: 4px;
                padding: 8px;
                margin: 2px;
            }
            QToolButton:hover {
                background-color: #f5f5f5;
                border: 1px solid #1976d2;
            }
            QToolButton:pressed {
                background-color: #e3f2fd;
            }
        """)
        
        # 添加工具栏按钮
        actions = [
            ("🏠", "首页", self.go_home),
            ("➕", "新建", self.new_document),
            ("💾", "保存", self.save_document),
            ("🔄", "刷新", self.refresh),
            ("❓", "帮助", self.show_help)
        ]
        
        for icon, text, callback in actions:
            action = QAction(f"{icon} {text}", self)
            action.triggered.connect(callback)
            toolbar.addAction(action)
        
        # 添加搜索框
        toolbar.addSeparator()
        search_widget = QLineEdit()
        search_widget.setPlaceholderText("🔍 搜索功能、文档...")
        search_widget.setFixedWidth(300)
        search_widget.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border: 1px solid #1976d2;
                outline: none;
            }
        """)
        toolbar.addWidget(search_widget)
        
    def create_statusbar(self):
        """创建状态栏"""
        statusbar = self.statusBar()
        statusbar.setStyleSheet("""
            QStatusBar {
                background-color: #ffffff;
                border-top: 1px solid #e0e0e0;
                color: #666666;
                font-size: 12px;
            }
        """)
        
        # 左侧状态信息
        statusbar.showMessage("就绪")
        
        # 右侧状态信息
        time_label = QLabel("2024-01-01 12:00:00")
        time_label.setStyleSheet("margin-right: 10px;")
        statusbar.addPermanentWidget(time_label)
        
        user_label = QLabel("👤 用户名称")
        statusbar.addPermanentWidget(user_label)
        
    def on_navigation_changed(self, key):
        """导航切换事件"""
        nav_map = {
            "dashboard": 0,
            "chat": 1,
            "documents": 2,
            "analytics": 3,
            "tools": 4,
            "team": 5,
            "projects": 6,
            "settings": 7
        }
        
        index = nav_map.get(key, 0)
        self.work_area.setCurrentIndex(index)
        
        # 更新状态栏
        nav_names = {
            "dashboard": "工作台",
            "chat": "AI助手",
            "documents": "文档管理",
            "analytics": "数据分析",
            "tools": "工具箱",
            "team": "团队协作",
            "projects": "项目管理",
            "settings": "系统设置"
        }
        
        self.statusBar().showMessage(f"当前页面: {nav_names.get(key, '未知')}")
        
    def go_home(self):
        """返回首页"""
        self.work_area.setCurrentIndex(0)
        self.statusBar().showMessage("已返回工作台")
        
    def new_document(self):
        """新建文档"""
        QMessageBox.information(self, "新建", "新建文档功能")
        
    def save_document(self):
        """保存文档"""
        QMessageBox.information(self, "保存", "保存文档功能")
        
    def refresh(self):
        """刷新"""
        QMessageBox.information(self, "刷新", "刷新页面功能")
        
    def show_help(self):
        """显示帮助"""
        QMessageBox.information(self, "帮助", "这是一个现代化的工作助手应用\n\n功能包括：\n- 文档管理\n- 数据分析\n- 工具箱\n- 团队协作\n- 项目管理")


class Application(QApplication):
    """应用程序类"""
    
    def __init__(self, argv):
        super().__init__(argv)
        self.setApplicationName("工作助手")
        self.setApplicationVersion("1.0.0")
        
        # 设置全局样式
        self.setStyleSheet("""
            * {
                font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
            }
            
            QWidget {
                background-color: #ffffff;
            }
            
            QScrollBar:vertical {
                background: #f0f0f0;
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #a0a0a0;
            }
        """)


def main():
    """主函数"""
    app = Application(sys.argv)
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main() 