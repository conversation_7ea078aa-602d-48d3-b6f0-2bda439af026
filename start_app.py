#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI文档助手应用启动器
一键启动豆包包工助手风格的PyQt6应用
"""

import sys
import os
import traceback

def check_dependencies():
    """检查依赖项"""
    print("🔍 正在检查依赖项...")
    
    missing_deps = []
    
    try:
        import PyQt6
        print("✅ PyQt6: 已安装")
    except ImportError:
        missing_deps.append("PyQt6")
        print("❌ PyQt6: 未安装")
    
    try:
        import win32com.client
        print("✅ pywin32: 已安装")
    except ImportError:
        print("⚠️ pywin32: 未安装 (WPS功能可能受限)")
    
    if missing_deps:
        print(f"\n❌ 缺少必要依赖项: {', '.join(missing_deps)}")
        print("请运行以下命令安装:")
        for dep in missing_deps:
            print(f"  pip install {dep}")
        return False
    
    print("✅ 所有必要依赖项已安装")
    return True

def main():
    """主启动函数"""
    print("🚀 AI文档助手启动器")
    print("=" * 40)
    
    # 检查依赖项
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    print("\n🎯 正在启动应用程序...")
    
    try:
        # 添加当前目录到Python路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 导入并启动应用
        from main_app import main as app_main
        
        print("✅ 应用程序启动中...")
        app_main()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有Python文件都在同一目录下")
        traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n详细错误信息:")
        traceback.print_exc()
        
    finally:
        input("\n按回车键退出...")

if __name__ == "__main__":
    main() 