# -*- coding: utf-8 -*-
"""
聊天对话组件 - 与AI Agent交互的界面
支持文档操作指令的自然语言交互
"""

import sys
import json
from datetime import datetime
from typing import Dict, Any, List
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

# 导入项目中的Agent组件
try:
    from wps_tool import SmartWPSAgent
    from intelligent_agent_v2 import *
    from enhanced_document_planner import *
    AGENT_AVAILABLE = True
except ImportError:
    AGENT_AVAILABLE = False
    print("⚠️ Agent模块未找到，聊天功能将使用模拟模式")


class MessageBubble(QWidget):
    """消息气泡组件"""
    
    def __init__(self, message: str, is_user: bool = True, timestamp: str = None):
        super().__init__()
        self.message = message
        self.is_user = is_user
        self.timestamp = timestamp or datetime.now().strftime("%H:%M")
        self.init_ui()
        
    def init_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(10, 5, 10, 5)
        
        if self.is_user:
            # 用户消息 - 右对齐，蓝色
            layout.addStretch()
            bubble = self.create_bubble("#1976d2", "#ffffff", "right")
            layout.addWidget(bubble, 0, Qt.AlignmentFlag.AlignRight)
        else:
            # AI消息 - 左对齐，灰色
            bubble = self.create_bubble("#f5f5f5", "#333333", "left")
            layout.addWidget(bubble, 0, Qt.AlignmentFlag.AlignLeft)
            layout.addStretch()
        
        self.setLayout(layout)
        
    def create_bubble(self, bg_color: str, text_color: str, align: str):
        """创建消息气泡"""
        container = QWidget()
        container.setMaximumWidth(600)
        
        # 气泡样式
        border_radius = "border-radius: 18px 18px" + (" 4px 18px;" if align == "right" else " 18px 4px;")
        container.setStyleSheet(f"""
            QWidget {{
                background-color: {bg_color};
                {border_radius}
                padding: 12px 16px;
                margin: 2px;
            }}
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)
        
        # 消息文本
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        message_label.setStyleSheet(f"""
            QLabel {{
                color: {text_color};
                font-size: 14px;
                line-height: 1.4;
                border: none;
                background: transparent;
            }}
        """)
        message_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        # 时间戳
        time_label = QLabel(self.timestamp)
        time_label.setStyleSheet(f"""
            QLabel {{
                color: {text_color if bg_color != "#f5f5f5" else "#888888"};
                font-size: 11px;
                border: none;
                background: transparent;
            }}
        """)
        time_label.setAlignment(Qt.AlignmentFlag.AlignRight if align == "right" else Qt.AlignmentFlag.AlignLeft)
        
        layout.addWidget(message_label)
        layout.addWidget(time_label)
        container.setLayout(layout)
        
        return container


class TypingIndicator(QWidget):
    """正在输入指示器"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
        # 动画定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_dots)
        self.dots_count = 0
        
    def init_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(10, 5, 10, 5)
        
        # AI头像
        avatar = QLabel("🤖")
        avatar.setFixedSize(32, 32)
        avatar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        avatar.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border-radius: 16px;
                font-size: 16px;
            }
        """)
        
        # 输入提示气泡
        self.bubble = QWidget()
        self.bubble.setFixedHeight(45)
        self.bubble.setMaximumWidth(120)
        self.bubble.setStyleSheet("""
            QWidget {
                background-color: #f5f5f5;
                border-radius: 18px 18px 18px 4px;
                padding: 12px 16px;
            }
        """)
        
        bubble_layout = QHBoxLayout()
        bubble_layout.setContentsMargins(0, 0, 0, 0)
        
        self.dots_label = QLabel("AI正在思考")
        self.dots_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 14px;
                background: transparent;
                border: none;
            }
        """)
        bubble_layout.addWidget(self.dots_label)
        self.bubble.setLayout(bubble_layout)
        
        layout.addWidget(avatar)
        layout.addWidget(self.bubble)
        layout.addStretch()
        
        self.setLayout(layout)
        
    def start_animation(self):
        """开始动画"""
        self.timer.start(500)  # 500ms间隔
        
    def stop_animation(self):
        """停止动画"""
        self.timer.stop()
        
    def update_dots(self):
        """更新点点点动画"""
        self.dots_count = (self.dots_count + 1) % 4
        dots = "." * self.dots_count
        self.dots_label.setText(f"AI正在思考{dots}")


class QuickActions(QWidget):
    """快速操作按钮"""
    
    action_clicked = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(10)
        
        # 快速操作按钮
        actions = [
            ("📝", "创建文档"),
            ("📊", "生成报表"), 
            ("🔄", "转换格式"),
            ("📋", "提取内容"),
            ("✏️", "编辑文档"),
            ("💾", "保存文档")
        ]
        
        for icon, text in actions:
            btn = QPushButton(f"{icon} {text}")
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 20px;
                    padding: 8px 16px;
                    font-size: 13px;
                    color: #495057;
                }
                QPushButton:hover {
                    background-color: #e9ecef;
                    border-color: #1976d2;
                    color: #1976d2;
                }
                QPushButton:pressed {
                    background-color: #dee2e6;
                }
            """)
            btn.clicked.connect(lambda checked, t=text: self.action_clicked.emit(t))
            layout.addWidget(btn)
        
        layout.addStretch()
        self.setLayout(layout)


class AgentWorker(QThread):
    """Agent工作线程"""
    
    response_ready = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, message: str):
        super().__init__()
        self.message = message
        
    def run(self):
        """在后台线程中处理Agent响应"""
        try:
            if AGENT_AVAILABLE:
                # 使用真实的Agent
                agent = SmartWPSAgent()
                result = agent.process_natural_language(self.message)
                
                if result.get("success"):
                    response = f"✅ 操作成功！\n\n{self.format_result(result)}"
                else:
                    response = f"❌ 操作失败：{result.get('error', '未知错误')}"
                    if "suggestion" in result:
                        response += f"\n\n💡 建议：{result['suggestion']}"
            else:
                # 模拟响应
                response = self.simulate_agent_response()
                
            self.response_ready.emit(response)
            
        except Exception as e:
            self.error_occurred.emit(f"处理出错：{str(e)}")
            
    def format_result(self, result: Dict[str, Any]) -> str:
        """格式化Agent响应结果"""
        if "document_name" in result:
            return f"文档操作：{result['document_name']}"
        elif "file_path" in result:
            return f"文件操作：{result['file_path']}"
        elif "replaced_count" in result:
            return f"替换了 {result['replaced_count']} 处内容"
        elif "table_index" in result:
            return f"创建了第 {result['table_index']} 个表格"
        else:
            return "操作已完成"
            
    def simulate_agent_response(self) -> str:
        """模拟Agent响应（当真实Agent不可用时）"""
        message_lower = self.message.lower()
        
        if "创建" in message_lower and "文档" in message_lower:
            return "✅ 模拟创建文档成功！\n\n📄 已创建新的Word文档\n📍 位置：新建文档.docx\n⏰ 创建时间：刚刚"
        elif "表格" in message_lower:
            return "✅ 模拟创建表格成功！\n\n📊 已插入3行4列表格\n📝 位置：文档末尾\n🎨 应用了默认样式"
        elif "保存" in message_lower:
            return "✅ 模拟保存文档成功！\n\n💾 文件已保存\n📁 格式：Word文档(.docx)\n📊 大小：约128KB"
        elif "分析" in message_lower:
            return "✅ 模拟文档分析完成！\n\n📄 文档统计：\n• 总页数：3页\n• 字符数：1,250字\n• 表格数：2个\n• 图片数：1张"
        else:
            return f"🤖 收到您的指令：{self.message}\n\n📋 这是模拟响应，请安装完整的Agent模块以获得真实功能。\n\n💡 您可以尝试说：\n• 创建一个新文档\n• 添加一个表格\n• 保存文档\n• 分析文档内容"


class ChatWidget(QWidget):
    """聊天对话主组件"""
    
    def __init__(self):
        super().__init__()
        self.chat_history = []
        self.typing_indicator = None
        self.agent_worker = None
        self.init_ui()
        self.add_welcome_message()
        
    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 聊天标题栏
        header = self.create_header()
        layout.addWidget(header)
        
        # 聊天消息区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #ffffff;
            }
            QScrollBar:vertical {
                background: #f0f0f0;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #c0c0c0;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #a0a0a0;
            }
        """)
        
        # 消息容器
        self.messages_widget = QWidget()
        self.messages_layout = QVBoxLayout()
        self.messages_layout.setContentsMargins(0, 10, 0, 10)
        self.messages_layout.setSpacing(8)
        self.messages_layout.addStretch()
        self.messages_widget.setLayout(self.messages_layout)
        
        self.scroll_area.setWidget(self.messages_widget)
        layout.addWidget(self.scroll_area, 1)
        
        # 快速操作
        self.quick_actions = QuickActions()
        self.quick_actions.action_clicked.connect(self.on_quick_action)
        layout.addWidget(self.quick_actions)
        
        # 输入区域
        input_area = self.create_input_area()
        layout.addWidget(input_area)
        
        self.setLayout(layout)
        
    def create_header(self):
        """创建聊天头部"""
        header = QWidget()
        header.setFixedHeight(60)
        header.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                border-bottom: 1px solid #e0e0e0;
            }
        """)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(20, 0, 20, 0)
        
        # AI助手信息
        avatar = QLabel("🤖")
        avatar.setFixedSize(40, 40)
        avatar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        avatar.setStyleSheet("""
            QLabel {
                background-color: #1976d2;
                border-radius: 20px;
                color: white;
                font-size: 20px;
            }
        """)
        
        info_layout = QVBoxLayout()
        info_layout.setSpacing(2)
        
        name_label = QLabel("AI文档助手")
        name_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #333333;
            }
        """)
        
        status_label = QLabel("🟢 在线 - 随时为您服务")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #666666;
            }
        """)
        
        info_layout.addWidget(name_label)
        info_layout.addWidget(status_label)
        
        layout.addWidget(avatar)
        layout.addLayout(info_layout)
        layout.addStretch()
        
        # 功能按钮
        help_btn = QPushButton("💡 帮助")
        help_btn.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 13px;
                color: #666666;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                color: #1976d2;
            }
        """)
        help_btn.clicked.connect(self.show_help)
        
        clear_btn = QPushButton("🗑️ 清空")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 13px;
                color: #666666;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                color: #dc3545;
            }
        """)
        clear_btn.clicked.connect(self.clear_chat)
        
        layout.addWidget(help_btn)
        layout.addWidget(clear_btn)
        
        header.setLayout(layout)
        return header
        
    def create_input_area(self):
        """创建输入区域"""
        input_area = QWidget()
        input_area.setFixedHeight(80)
        input_area.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                border-top: 1px solid #e0e0e0;
            }
        """)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        
        # 输入框
        self.input_field = QTextEdit()
        self.input_field.setFixedHeight(50)
        self.input_field.setPlaceholderText("💬 输入您的指令，比如：创建一个工作报告文档...")
        self.input_field.setStyleSheet("""
            QTextEdit {
                border: 1px solid #e0e0e0;
                border-radius: 25px;
                padding: 12px 20px;
                font-size: 14px;
                background-color: #f8f9fa;
            }
            QTextEdit:focus {
                border: 2px solid #1976d2;
                background-color: #ffffff;
                outline: none;
            }
        """)
        
        # 发送按钮
        self.send_button = QPushButton("🚀")
        self.send_button.setFixedSize(50, 50)
        self.send_button.setStyleSheet("""
            QPushButton {
                background-color: #1976d2;
                border: none;
                border-radius: 25px;
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.send_button.clicked.connect(self.send_message)
        
        layout.addWidget(self.input_field)
        layout.addWidget(self.send_button)
        
        input_area.setLayout(layout)
        
        # 绑定快捷键
        self.input_field.installEventFilter(self)
        
        return input_area
        
    def eventFilter(self, obj, event):
        """事件过滤器 - 处理快捷键"""
        if obj == self.input_field and event.type() == QEvent.Type.KeyPress:
            if event.key() == Qt.Key.Key_Return and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                self.send_message()
                return True
        return super().eventFilter(obj, event)
        
    def add_welcome_message(self):
        """添加欢迎消息"""
        welcome_text = """👋 您好！我是AI文档助手

我可以帮您：
• 📝 创建和编辑Word文档
• 📊 生成表格和图表
• 🔄 转换文档格式
• 📋 提取和分析内容
• 💾 保存和管理文件

💡 您可以用自然语言告诉我要做什么，比如：
"创建一个会议纪要文档"
"在文档中添加一个销售数据表格"
"把这个文档转换为PDF格式"

🚀 试试快速操作按钮，或直接输入您的需求！"""
        
        self.add_message(welcome_text, is_user=False)
        
    def add_message(self, message: str, is_user: bool = True):
        """添加消息到聊天记录"""
        # 移除stretch
        item = self.messages_layout.takeAt(self.messages_layout.count() - 1)
        if item:
            # 检查是否是widget，如果是则删除widget
            if item.widget():
                item.widget().deleteLater()
            # 如果是spacer，不需要特殊处理，item会自动被清理
        
        # 添加消息气泡
        bubble = MessageBubble(message, is_user)
        self.messages_layout.addWidget(bubble)
        
        # 重新添加stretch
        self.messages_layout.addStretch()
        
        # 滚动到底部
        QTimer.singleShot(50, self.scroll_to_bottom)
        
        # 保存到历史记录
        self.chat_history.append({
            "message": message,
            "is_user": is_user,
            "timestamp": datetime.datetime.now().isoformat()
        })
        
    def scroll_to_bottom(self):
        """滚动到底部"""
        scrollbar = self.scroll_area.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def send_message(self):
        """发送消息"""
        message = self.input_field.toPlainText().strip()
        if not message:
            return
            
        # 清空输入框
        self.input_field.clear()
        
        # 添加用户消息
        self.add_message(message, is_user=True)
        
        # 显示输入指示器
        self.show_typing_indicator()
        
        # 禁用发送按钮
        self.send_button.setEnabled(False)
        
        # 启动Agent工作线程
        self.agent_worker = AgentWorker(message)
        self.agent_worker.response_ready.connect(self.on_agent_response)
        self.agent_worker.error_occurred.connect(self.on_agent_error)
        self.agent_worker.finished.connect(self.on_worker_finished)
        self.agent_worker.start()
        
    def show_typing_indicator(self):
        """显示正在输入指示器"""
        if self.typing_indicator:
            return
            
        # 移除stretch
        item = self.messages_layout.takeAt(self.messages_layout.count() - 1)
        if item:
            # 检查是否是widget，如果是则删除widget
            if item.widget():
                item.widget().deleteLater()
            # 如果是spacer，不需要特殊处理，item会自动被清理
            
        self.typing_indicator = TypingIndicator()
        self.messages_layout.addWidget(self.typing_indicator)
        
        # 重新添加stretch
        self.messages_layout.addStretch()
        
        self.typing_indicator.start_animation()
        self.scroll_to_bottom()
        
    def hide_typing_indicator(self):
        """隐藏正在输入指示器"""
        if self.typing_indicator:
            self.typing_indicator.stop_animation()
            self.messages_layout.removeWidget(self.typing_indicator)
            self.typing_indicator.deleteLater()
            self.typing_indicator = None
            
    def on_agent_response(self, response: str):
        """处理Agent响应"""
        self.hide_typing_indicator()
        self.add_message(response, is_user=False)
        
    def on_agent_error(self, error: str):
        """处理Agent错误"""
        self.hide_typing_indicator()
        error_msg = f"❌ 抱歉，处理您的请求时出现错误：\n\n{error}\n\n💡 请尝试重新描述您的需求，或点击快速操作按钮。"
        self.add_message(error_msg, is_user=False)
        
    def on_worker_finished(self):
        """工作线程完成"""
        self.send_button.setEnabled(True)
        if self.agent_worker:
            self.agent_worker.deleteLater()
            self.agent_worker = None
            
    def on_quick_action(self, action: str):
        """处理快速操作"""
        action_map = {
            "创建文档": "创建一个新的工作文档",
            "生成报表": "生成一个数据分析报表",
            "转换格式": "将当前文档转换为PDF格式",
            "提取内容": "提取文档中的主要内容",
            "编辑文档": "帮我编辑当前文档",
            "保存文档": "保存当前文档"
        }
        
        message = action_map.get(action, action)
        self.input_field.setPlainText(message)
        self.send_message()
        
    def show_help(self):
        """显示帮助信息"""
        help_text = """🔍 使用指南

📝 **文档操作命令示例：**
• "创建一个会议纪要"
• "添加标题：月度工作总结"
• "插入一个3行4列的表格"
• "保存文档到桌面，文件名为报告.docx"

🎨 **格式化命令：**
• "将标题设为加粗，16号字"
• "设置段落居中对齐"
• "添加页眉页脚"

📊 **表格操作：**
• "创建销售数据表格"
• "在表格第一行添加：姓名、部门、销售额"
• "设置表格样式为专业风格"

💾 **文件操作：**
• "保存为PDF格式"
• "导出文档内容"
• "分析文档结构"

💡 **提示：**
• 使用Ctrl+Enter快速发送
• 点击快速操作按钮获得灵感
• 描述越具体，结果越准确"""

        self.add_message(help_text, is_user=False)
        
    def clear_chat(self):
        """清空聊天记录"""
        reply = QMessageBox.question(
            self, 
            "清空聊天", 
            "确定要清空所有聊天记录吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 清空消息布局
            while self.messages_layout.count() > 1:  # 保留stretch
                item = self.messages_layout.takeAt(0)
                if item and item.widget():
                    item.widget().deleteLater()
                    
            # 清空历史记录
            self.chat_history.clear()
            
            # 重新添加欢迎消息
            self.add_welcome_message()
            
    def get_chat_history(self) -> List[Dict[str, Any]]:
        """获取聊天历史记录"""
        return self.chat_history
        
    def save_chat_history(self, file_path: str):
        """保存聊天记录到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.chat_history, f, ensure_ascii=False, indent=2)
            QMessageBox.information(self, "保存成功", f"聊天记录已保存到：{file_path}")
        except Exception as e:
            QMessageBox.warning(self, "保存失败", f"保存聊天记录时出错：{str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        * {
            font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
        }
    """)
    
    # 创建测试窗口
    window = QMainWindow()
    window.setWindowTitle("AI文档助手 - 聊天测试")
    window.setGeometry(100, 100, 800, 600)
    
    chat_widget = ChatWidget()
    window.setCentralWidget(chat_widget)
    
    window.show()
    sys.exit(app.exec()) 