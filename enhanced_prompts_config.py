# -*- coding: utf-8 -*-
"""
增强版提示词配置 - 支持丰富样式和主题功能

包含：
- 文档类型识别和样式映射
- 主题选择逻辑
- 增强的工具定义
- 通用文档创建提示词
"""

from typing import Dict, Any, List
import json

class EnhancedPromptsConfig:
    """增强版提示词配置类"""
    
    # 文档类型与主题的智能映射
    DOCUMENT_THEME_MAPPING = {
        "商务报告": "professional",
        "技术文档": "modern", 
        "学术论文": "academic",
        "用户手册": "modern",
        "项目计划": "professional",
        "会议纪要": "professional",
        "产品介绍": "creative",
        "培训手册": "elegant",
        "法律文件": "academic",
        "财务报告": "professional",
        "营销方案": "creative",
        "研究报告": "academic",
        "操作指南": "modern",
        "公司制度": "professional",
        "设计方案": "creative",
        "测试文档": "modern",
        "合同文件": "elegant",
        "简历模板": "elegant",
        "邀请函": "elegant",
        "通知公告": "professional"
    }
    
    # 文档类型的样式特征
    DOCUMENT_STYLE_FEATURES = {
        "商务报告": {
            "title_style": {"font_size": 20, "bold": True, "color": "79,129,189", "alignment": "center"},
            "heading_style": {"font_size": 16, "bold": True, "color": "79,129,189"},
            "body_style": {"font_size": 12, "font_name": "宋体", "line_spacing": 1.5},
            "use_page_numbers": True,
            "header_text": "商务报告",
            "include_toc": True
        },
        "技术文档": {
            "title_style": {"font_size": 18, "bold": True, "color": "64,64,64", "alignment": "left"},
            "heading_style": {"font_size": 14, "bold": True, "color": "64,64,64"},
            "body_style": {"font_size": 11, "font_name": "微软雅黑"},
            "code_style": {"font_name": "Consolas", "highlight_color": "240,240,240"},
            "use_page_numbers": True,
            "include_toc": True
        },
        "学术论文": {
            "title_style": {"font_size": 16, "bold": True, "color": "0,0,0", "alignment": "center"},
            "heading_style": {"font_size": 14, "bold": True, "color": "0,0,0"},
            "body_style": {"font_size": 12, "font_name": "Times New Roman", "line_spacing": 2.0},
            "use_page_numbers": True,
            "first_page_different": True,
            "margin_adjustments": {"top": 2.5, "bottom": 2.5, "left": 3.0, "right": 3.0}
        },
        "创意文档": {
            "title_style": {"font_size": 22, "bold": True, "color": "247,150,70", "alignment": "center"},
            "heading_style": {"font_size": 16, "bold": True, "text_effect": "shadow"},
            "body_style": {"font_size": 12, "font_name": "微软雅黑"},
            "use_colors": True,
            "decorative_elements": True
        },
        "正式文件": {
            "title_style": {"font_size": 18, "bold": True, "color": "0,0,0", "alignment": "center"},
            "heading_style": {"font_size": 14, "bold": True, "color": "0,0,0"},
            "body_style": {"font_size": 12, "font_name": "宋体", "line_spacing": 1.5},
            "use_page_numbers": True,
            "footer_text": "此文件为正式版本",
            "border_style": "single"
        }
    }
    
    @staticmethod
    def get_system_prompt() -> str:
        """获取增强的系统提示词"""
        return """你是一个专业的智能文档处理助手，具备强大的文档创建和样式设计能力。

## 🎯 核心能力

### 📝 文档创建能力
- 创建各种类型的专业文档（商务报告、技术文档、学术论文、用户手册等）
- 智能选择合适的文档主题和样式
- 生成结构化、内容丰富的文档

### 🎨 样式设计能力
- **6种专业主题**: professional, modern, elegant, academic, creative, minimal
- **丰富文本样式**: 字体、颜色、大小、特效（阴影、浮雕、描边等）
- **段落格式控制**: 行间距、缩进、对齐、边框、底纹
- **页面布局**: 页面大小、方向、边距、装订线
- **页眉页脚**: 灵活的页码格式、首页不同、奇偶页不同
- **表格样式**: 专业的表格设计和数据展示

### 🧠 智能分析能力
- 分析用户需求，识别文档类型和风格要求
- 自动选择最合适的主题和样式组合
- 根据内容特点调整格式和布局

## 📋 工作流程

### 阶段1: 需求分析
1. **理解用户意图** - 文档类型、内容要求、样式偏好
2. **选择文档主题** - 根据文档用途选择最佳主题
3. **设计样式方案** - 确定字体、颜色、布局等详细样式
4. **制定执行计划** - 生成具体的创建步骤

### 阶段2: 文档创建
1. **创建文档基础** - 设置页面和主题
2. **添加内容结构** - 标题、章节、段落
3. **应用样式格式** - 文本样式、段落格式
4. **完善文档元素** - 表格、页眉页脚、页码
5. **最终调整** - 检查格式，完善细节

## 🎨 主题选择指南

- **🏢 Professional** - 商务报告、项目计划、公司文件
- **🎯 Modern** - 技术文档、用户手册、操作指南  
- **💎 Elegant** - 合同文件、邀请函、简历模板
- **📚 Academic** - 学术论文、研究报告、法律文件
- **🎨 Creative** - 产品介绍、营销方案、设计文档
- **⚡ Minimal** - 简洁文档、技术规范、内部备忘

## 💡 样式设计原则

1. **功能优先** - 样式服务于内容和目的
2. **一致性** - 保持整个文档的视觉统一
3. **可读性** - 确保内容清晰易读
4. **专业性** - 符合行业和用途标准
5. **美观性** - 视觉上令人愉悦

## 🛠️ 可用工具功能

### 基础文档操作
- `create_document` - 创建文档
- `add_text` - 添加文本（支持丰富样式）
- `create_table` - 创建表格
- `insert_image` - 插入图片
- `save_document` - 保存文档

### 高级样式功能  
- `set_page_style` - 设置页面样式（大小、方向、边距）
- `set_header_footer` - 设置页眉页脚（支持页码、首页不同）
- `apply_document_theme` - 应用文档主题
- `format_text` - 格式化文本样式
- `set_document_metadata` - 设置文档属性

### 内容生成
- `generate_detailed_content` - 生成详细内容（支持指定字数）

## 📋 响应模式

当用户提出文档创建需求时，你需要：

1. **分析需求** - 识别文档类型、风格要求、内容需求
2. **制定方案** - 选择主题、设计样式、规划结构  
3. **生成计划** - 详细的执行步骤和参数配置
4. **执行创建** - 调用相应工具完成文档创建

始终以JSON格式输出执行计划，确保结构化和可执行性。
用中文与用户交流，保持友好和专业的沟通风格。

记住：你的目标是创建既专业又美观的文档，让用户的内容以最佳方式呈现！🎯"""

    @staticmethod
    def get_document_analysis_prompt() -> str:
        """获取文档分析阶段的提示词"""
        return """请分析用户的文档创建需求，并生成详细的文档计划。

## 分析要点

1. **文档类型识别**
   - 商务报告、技术文档、学术论文、用户手册等
   - 根据关键词和描述判断文档性质

2. **样式风格选择**
   - 根据文档类型选择合适的主题 (professional/modern/elegant/academic/creative/minimal)
   - 考虑用户的风格偏好和行业特点

3. **内容结构规划**
   - 确定章节结构和内容要点
   - 计算合理的字数分配
   - 规划表格、图片等元素位置

4. **格式设计方案**
   - 选择字体、颜色、大小等文本样式
   - 设计页面布局和页眉页脚
   - 确定段落格式和间距

## 输出要求

以JSON格式输出完整的文档计划，包含：
- document_type: 文档类型
- theme_name: 选择的主题
- style_config: 详细样式配置
- content_structure: 内容结构
- execution_steps: 具体执行步骤

确保计划详细、可执行，包含所有必要的参数和配置信息。"""

    @staticmethod
    def get_enhanced_tools_definition() -> List[Dict[str, Any]]:
        """获取增强的工具定义"""
        return [
            {
                "type": "function",
                "function": {
                    "name": "create_document",
                    "description": "创建新的WPS文档，可以基于模板或创建空白文档",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "template_path": {
                                "type": "string",
                                "description": "模板文件路径（可选）"
                            },
                            "title": {
                                "type": "string",
                                "description": "文档标题（可选）"
                            }
                        },
                        "required": []
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "add_text",
                    "description": "向文档添加文本内容，支持丰富的样式设置",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "要添加的文本内容"
                            },
                            "position": {
                                "type": "string",
                                "description": "插入位置：start（开头）、end（结尾）",
                                "enum": ["start", "end"]
                            },
                            "font_name": {
                                "type": "string",
                                "description": "字体名称，如：宋体、微软雅黑、Times New Roman"
                            },
                            "font_size": {
                                "type": "integer",
                                "description": "字体大小（磅）",
                                "minimum": 8,
                                "maximum": 72
                            },
                            "bold": {
                                "type": "boolean",
                                "description": "是否加粗"
                            },
                            "italic": {
                                "type": "boolean",
                                "description": "是否斜体"
                            },
                            "underline": {
                                "type": "boolean",
                                "description": "是否下划线"
                            },
                            "strikethrough": {
                                "type": "boolean",
                                "description": "是否删除线"
                            },
                            "color": {
                                "type": "string",
                                "description": "文字颜色，RGB格式如：255,0,0（红色）"
                            },
                            "highlight_color": {
                                "type": "string",
                                "description": "高亮背景色，RGB格式如：255,255,0（黄色）"
                            },
                            "alignment": {
                                "type": "string",
                                "description": "对齐方式",
                                "enum": ["left", "center", "right", "justify", "distribute"]
                            },
                            "text_effect": {
                                "type": "string",
                                "description": "文字特效",
                                "enum": ["shadow", "outline", "emboss", "engrave"]
                            },
                            "vertical_position": {
                                "type": "string",
                                "description": "垂直位置",
                                "enum": ["normal", "superscript", "subscript"]
                            },
                            "character_spacing": {
                                "type": "number",
                                "description": "字符间距（磅）"
                            }
                        },
                        "required": ["text"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "set_page_style",
                    "description": "设置页面样式，包括页面大小、方向、边距等",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "page_size": {
                                "type": "string",
                                "description": "页面大小",
                                "enum": ["A4", "A3", "A5", "Letter", "Legal", "custom"]
                            },
                            "orientation": {
                                "type": "string",
                                "description": "页面方向",
                                "enum": ["portrait", "landscape"]
                            },
                            "margin_top": {
                                "type": "number",
                                "description": "上边距（厘米）"
                            },
                            "margin_bottom": {
                                "type": "number",
                                "description": "下边距（厘米）"
                            },
                            "margin_left": {
                                "type": "number",
                                "description": "左边距（厘米）"
                            },
                            "margin_right": {
                                "type": "number",
                                "description": "右边距（厘米）"
                            },
                            "custom_width": {
                                "type": "number",
                                "description": "自定义页面宽度（厘米），仅当page_size为custom时使用"
                            },
                            "custom_height": {
                                "type": "number",
                                "description": "自定义页面高度（厘米），仅当page_size为custom时使用"
                            }
                        }
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "set_header_footer",
                    "description": "设置页眉页脚，支持页码、首页不同、奇偶页不同等高级功能",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "header_text": {
                                "type": "string",
                                "description": "页眉文本内容"
                            },
                            "footer_text": {
                                "type": "string",
                                "description": "页脚文本内容"
                            },
                            "include_page_number": {
                                "type": "boolean",
                                "description": "是否包含页码"
                            },
                            "page_number_format": {
                                "type": "string",
                                "description": "页码格式",
                                "enum": ["arabic", "roman_upper", "roman_lower", "alpha_upper", "alpha_lower"]
                            },
                            "page_number_position": {
                                "type": "string",
                                "description": "页码位置",
                                "enum": ["header_left", "header_center", "header_right", "footer_left", "footer_center", "footer_right"]
                            },
                            "first_page_different": {
                                "type": "boolean",
                                "description": "首页是否使用不同的页眉页脚"
                            },
                            "odd_even_different": {
                                "type": "boolean",
                                "description": "奇偶页是否使用不同的页眉页脚"
                            },
                            "border_line": {
                                "type": "boolean",
                                "description": "是否显示页眉页脚边框线"
                            }
                        }
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "apply_document_theme",
                    "description": "应用文档主题，一键设置整体文档风格",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "theme_name": {
                                "type": "string",
                                "description": "主题名称",
                                "enum": ["professional", "modern", "elegant", "academic", "creative", "minimal"]
                            },
                            "primary_color": {
                                "type": "string",
                                "description": "主色调，RGB格式如：79,129,189"
                            },
                            "secondary_color": {
                                "type": "string",
                                "description": "辅助色，RGB格式"
                            },
                            "heading_font": {
                                "type": "string",
                                "description": "标题字体"
                            },
                            "body_font": {
                                "type": "string",
                                "description": "正文字体"
                            }
                        },
                        "required": ["theme_name"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "create_table",
                    "description": "创建表格，支持数据填充和样式设置",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "rows": {
                                "type": "integer",
                                "description": "表格行数",
                                "minimum": 1
                            },
                            "cols": {
                                "type": "integer",
                                "description": "表格列数",
                                "minimum": 1
                            },
                            "data": {
                                "type": "array",
                                "description": "表格数据（二维数组）",
                                "items": {
                                    "type": "array",
                                    "items": {"type": "string"}
                                }
                            },
                            "caption": {
                                "type": "string",
                                "description": "表格标题"
                            },
                            "style": {
                                "type": "string",
                                "description": "表格样式名称"
                            }
                        },
                        "required": ["rows", "cols"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "save_document",
                    "description": "保存文档到指定路径，支持多种格式",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "file_path": {
                                "type": "string",
                                "description": "保存的文件路径"
                            },
                            "format_type": {
                                "type": "string",
                                "description": "文件格式",
                                "enum": ["docx", "doc", "pdf", "rtf", "txt", "html"]
                            }
                        },
                        "required": ["file_path"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "set_document_metadata",
                    "description": "设置文档元数据信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "title": {"type": "string", "description": "文档标题"},
                            "author": {"type": "string", "description": "作者"},
                            "subject": {"type": "string", "description": "主题"},
                            "keywords": {"type": "string", "description": "关键词"},
                            "description": {"type": "string", "description": "描述"},
                            "company": {"type": "string", "description": "公司"}
                        }
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "add_paragraph_with_format",
                    "description": "添加带有高级段落格式的文本",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "段落文本内容"
                            },
                            "line_spacing": {
                                "type": "number",
                                "description": "行间距倍数，如1.5表示1.5倍行距"
                            },
                            "space_before": {
                                "type": "number",
                                "description": "段前间距（磅）"
                            },
                            "space_after": {
                                "type": "number",
                                "description": "段后间距（磅）"
                            },
                            "first_line_indent": {
                                "type": "number",
                                "description": "首行缩进（磅）"
                            },
                            "left_indent": {
                                "type": "number",
                                "description": "左缩进（磅）"
                            },
                            "right_indent": {
                                "type": "number",
                                "description": "右缩进（磅）"
                            },
                            "border_style": {
                                "type": "string",
                                "description": "边框样式",
                                "enum": ["none", "single", "double", "dotted", "dashed"]
                            },
                            "shading_color": {
                                "type": "string",
                                "description": "背景底纹颜色，RGB格式"
                            },
                            "keep_with_next": {
                                "type": "boolean",
                                "description": "与下段同页"
                            },
                            "page_break_before": {
                                "type": "boolean",
                                "description": "段前分页"
                            }
                        },
                        "required": ["text"]
                    }
                }
            }
        ]

    @staticmethod
    def get_document_type_suggestions(user_input: str) -> Dict[str, Any]:
        """根据用户输入智能推荐文档类型和样式"""
        user_input_lower = user_input.lower()
        
        # 关键词映射
        keyword_mapping = {
            "报告": "商务报告",
            "技术": "技术文档", 
            "论文": "学术论文",
            "手册": "用户手册",
            "计划": "项目计划",
            "会议": "会议纪要",
            "产品": "产品介绍",
            "培训": "培训手册",
            "合同": "法律文件",
            "财务": "财务报告",
            "营销": "营销方案",
            "研究": "研究报告",
            "操作": "操作指南",
            "制度": "公司制度",
            "设计": "设计方案",
            "测试": "测试文档",
            "简历": "简历模板",
            "邀请": "邀请函",
            "通知": "通知公告"
        }
        
        # 检测文档类型
        detected_type = "通用文档"
        for keyword, doc_type in keyword_mapping.items():
            if keyword in user_input_lower:
                detected_type = doc_type
                break
        
        # 获取对应的主题和样式
        theme = EnhancedPromptsConfig.DOCUMENT_THEME_MAPPING.get(detected_type, "professional")
        style_features = EnhancedPromptsConfig.DOCUMENT_STYLE_FEATURES.get(detected_type, {})
        
        return {
            "document_type": detected_type,
            "recommended_theme": theme,
            "style_features": style_features,
            "confidence": 0.8 if detected_type != "通用文档" else 0.3
        }

# 预定义的文档模板配置
ENHANCED_DOCUMENT_TEMPLATES = {
    "商务报告": {
        "theme": "professional",
        "structure": ["封面", "目录", "执行摘要", "背景分析", "数据展示", "结论建议", "附录"],
        "style_config": {
            "title": {"font_size": 20, "bold": True, "color": "79,129,189", "alignment": "center"},
            "heading1": {"font_size": 16, "bold": True, "color": "79,129,189"},
            "heading2": {"font_size": 14, "bold": True, "color": "79,129,189"},
            "body": {"font_size": 12, "font_name": "宋体", "line_spacing": 1.5}
        },
        "page_setup": {
            "page_size": "A4",
            "margins": {"top": 2.5, "bottom": 2.5, "left": 3.0, "right": 3.0}
        },
        "header_footer": {
            "include_page_number": True,
            "page_number_position": "footer_center",
            "header_text": "商务报告"
        }
    },
    
    "技术文档": {
        "theme": "modern",
        "structure": ["概述", "技术架构", "功能说明", "接口文档", "使用指南", "常见问题"],
        "style_config": {
            "title": {"font_size": 18, "bold": True, "color": "64,64,64"},
            "heading1": {"font_size": 14, "bold": True, "color": "64,64,64"},
            "code": {"font_name": "Consolas", "highlight_color": "240,240,240"},
            "body": {"font_size": 11, "font_name": "微软雅黑"}
        }
    },
    
    "学术论文": {
        "theme": "academic", 
        "structure": ["摘要", "引言", "文献综述", "研究方法", "结果分析", "讨论", "结论", "参考文献"],
        "style_config": {
            "title": {"font_size": 16, "bold": True, "alignment": "center"},
            "body": {"font_size": 12, "font_name": "Times New Roman", "line_spacing": 2.0}
        },
        "page_setup": {
            "margins": {"top": 2.5, "bottom": 2.5, "left": 3.0, "right": 3.0}
        },
        "header_footer": {
            "first_page_different": True,
            "include_page_number": True
        }
    }
} 