#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动脚本 - 豆包包工助手风格PyQt6应用
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from main_app import main
    
    if __name__ == "__main__":
        print("🚀 启动工作助手应用...")
        print("📋 检查依赖项...")
        
        try:
            import PyQt6
            print("✅ PyQt6 已安装")
        except ImportError:
            print("❌ PyQt6 未安装，请运行: pip install PyQt6")
            sys.exit(1)
        
        print("🎯 启动应用程序...")
        main()
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保所有依赖项已正确安装")
    print("运行命令: pip install -r requirements_pyqt.txt")
    sys.exit(1)
except Exception as e:
    print(f"❌ 启动失败: {e}")
    sys.exit(1) 